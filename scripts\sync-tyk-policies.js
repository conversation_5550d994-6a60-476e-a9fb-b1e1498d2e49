#!/usr/bin/env node

/**
 * Tyk Policy Sync Script
 * Synchronizes roles and permissions with Tyk policies
 */

const { Role, Permission, Identity, IdentityRole } = require('../models');
const tykPolicyManager = require('../utilities/tykPolicyManager');
const logger = require('../config/logger');
const config = require('../config/config');

class TykPolicySync {
  constructor() {
    this.syncResults = {
      rolesProcessed: 0,
      policiesCreated: 0,
      policiesUpdated: 0,
      policiesDeleted: 0,
      errors: []
    };
  }

  /**
   * Run full synchronization
   */
  async runSync() {
    console.log('🔄 Starting Tyk policy synchronization...\n');

    if (config.auth.mode !== 'tyk') {
      console.log('⚠️  AUTH mode is not set to "tyk". Sync may not be necessary.');
      return;
    }

    try {
      // Sync roles to policies
      await this.syncRolesToPolicies();

      // Clean up orphaned policies
      await this.cleanupOrphanedPolicies();

      // Print results
      this.printSyncResults();

    } catch (error) {
      console.error('❌ Sync failed:', error.message);
      logger.error('Policy sync failed:', error);
      process.exit(1);
    }
  }

  /**
   * Sync roles to Tyk policies
   */
  async syncRolesToPolicies() {
    console.log('📋 Synchronizing roles to Tyk policies...');

    try {
      const roles = await Role.findAll({
        where: { is_active: true },
        include: [
          {
            model: Permission,
            as: 'permission',
            through: { attributes: [] }
          }
        ]
      });

      for (const role of roles) {
        try {
          this.syncResults.rolesProcessed++;
          const permissions = role.permission || [];
          
          // Check if policy exists in cache
          const cachedPolicy = tykPolicyManager.getCachedPolicy(role.role_id);
          
          if (cachedPolicy) {
            // Update existing policy
            const result = await tykPolicyManager.updateRolePolicy(role, permissions);
            if (result) {
              this.syncResults.policiesUpdated++;
              console.log(`   ✅ Updated policy for role: ${role.name}`);
            }
          } else {
            // Create new policy
            const result = await tykPolicyManager.createRolePolicy(role, permissions);
            if (result) {
              this.syncResults.policiesCreated++;
              console.log(`   ✅ Created policy for role: ${role.name}`);
            }
          }

        } catch (error) {
          this.syncResults.errors.push({
            type: 'role_sync',
            item: role.name,
            error: error.message
          });
          console.log(`   ❌ Failed to sync role: ${role.name} - ${error.message}`);
        }
      }

      console.log(`✅ Role sync completed: ${this.syncResults.rolesProcessed} processed\n`);

    } catch (error) {
      console.error('❌ Role sync failed:', error.message);
      throw error;
    }
  }

  /**
   * Clean up orphaned policies
   */
  async cleanupOrphanedPolicies() {
    console.log('🧹 Cleaning up orphaned policies...');

    try {
      // Get all active role IDs
      const activeRoles = await Role.findAll({
        where: { is_active: true },
        attributes: ['role_id']
      });
      const activeRoleIds = new Set(activeRoles.map(r => r.role_id));

      // Check cached policies for orphaned entries
      const cachedPolicies = tykPolicyManager.policyCache;
      let orphanedCount = 0;

      for (const [roleId, policyData] of cachedPolicies.entries()) {
        if (!activeRoleIds.has(roleId)) {
          try {
            await tykPolicyManager.deleteRolePolicy(roleId);
            orphanedCount++;
            this.syncResults.policiesDeleted++;
            console.log(`   ✅ Deleted orphaned policy for role ID: ${roleId}`);
          } catch (error) {
            this.syncResults.errors.push({
              type: 'cleanup',
              item: roleId,
              error: error.message
            });
            console.log(`   ❌ Failed to delete orphaned policy: ${roleId} - ${error.message}`);
          }
        }
      }

      if (orphanedCount === 0) {
        console.log('   ✅ No orphaned policies found');
      }

      console.log(`✅ Cleanup completed: ${orphanedCount} orphaned policies removed\n`);

    } catch (error) {
      console.error('❌ Cleanup failed:', error.message);
      throw error;
    }
  }

  /**
   * Sync specific role
   */
  async syncRole(roleId) {
    console.log(`🔄 Syncing specific role: ${roleId}...`);

    try {
      const role = await Role.findByPk(roleId, {
        include: [
          {
            model: Permission,
            as: 'permission',
            through: { attributes: [] }
          }
        ]
      });

      if (!role) {
        throw new Error(`Role not found: ${roleId}`);
      }

      if (!role.is_active) {
        // Delete policy if role is inactive
        await tykPolicyManager.deleteRolePolicy(roleId);
        console.log(`✅ Deleted policy for inactive role: ${role.name}`);
        return;
      }

      const permissions = role.permission || [];
      const cachedPolicy = tykPolicyManager.getCachedPolicy(roleId);

      let result;
      if (cachedPolicy) {
        result = await tykPolicyManager.updateRolePolicy(role, permissions);
        console.log(`✅ Updated policy for role: ${role.name}`);
      } else {
        result = await tykPolicyManager.createRolePolicy(role, permissions);
        console.log(`✅ Created policy for role: ${role.name}`);
      }

      return result;

    } catch (error) {
      console.error(`❌ Failed to sync role ${roleId}:`, error.message);
      throw error;
    }
  }

  /**
   * Sync user API keys
   */
  async syncUserApiKeys(identityId = null) {
    console.log('🔑 Syncing user API keys...');

    try {
      let users;
      
      if (identityId) {
        users = await Identity.findAll({
          where: { identity_id: identityId },
          include: [{ model: Role, as: 'role' }]
        });
      } else {
        users = await Identity.findAll({
          include: [{ model: Role, as: 'role' }]
        });
      }

      let processedCount = 0;
      let errorCount = 0;

      for (const user of users) {
        try {
          const roles = user.role || [];
          
          if (roles.length === 0) {
            console.log(`   ⚠️  Skipping user ${user.email}: No roles assigned`);
            continue;
          }

          await tykPolicyManager.createIdentityApiKey(user, roles);
          processedCount++;
          console.log(`   ✅ Synced API key for user: ${user.email}`);

        } catch (error) {
          errorCount++;
          console.log(`   ❌ Failed to sync API key for user: ${user.email} - ${error.message}`);
        }
      }

      console.log(`✅ API key sync completed: ${processedCount} success, ${errorCount} failed\n`);

    } catch (error) {
      console.error('❌ API key sync failed:', error.message);
      throw error;
    }
  }

  /**
   * Print sync results
   */
  printSyncResults() {
    console.log('📊 Sync Results Summary:');
    console.log('=' * 40);

    console.log(`Roles Processed: ${this.syncResults.rolesProcessed}`);
    console.log(`Policies Created: ${this.syncResults.policiesCreated}`);
    console.log(`Policies Updated: ${this.syncResults.policiesUpdated}`);
    console.log(`Policies Deleted: ${this.syncResults.policiesDeleted}`);
    console.log(`Errors: ${this.syncResults.errors.length}`);

    if (this.syncResults.errors.length > 0) {
      console.log('\nErrors:');
      this.syncResults.errors.forEach(error => {
        console.log(`  - ${error.type}: ${error.item} - ${error.error}`);
      });
    }

    console.log('\n' + '=' * 40);
    
    if (this.syncResults.errors.length === 0) {
      console.log('🎉 Sync completed successfully!');
    } else {
      console.log('⚠️  Sync completed with some errors. Please review above.');
    }
  }

  /**
   * Validate sync status
   */
  async validateSync() {
    console.log('🔍 Validating sync status...');

    try {
      const activeRoles = await Role.count({ where: { is_active: true } });
      const cachedPolicies = tykPolicyManager.policyCache.size;

      console.log(`Active Roles: ${activeRoles}`);
      console.log(`Cached Policies: ${cachedPolicies}`);

      if (activeRoles === cachedPolicies) {
        console.log('✅ Sync status: All roles have corresponding policies');
      } else {
        console.log('⚠️  Sync status: Mismatch detected, consider running sync');
      }

      return activeRoles === cachedPolicies;

    } catch (error) {
      console.error('❌ Validation failed:', error.message);
      return false;
    }
  }
}

// CLI interface
if (require.main === module) {
  const sync = new TykPolicySync();
  
  const command = process.argv[2];
  const param = process.argv[3];
  
  switch (command) {
    case 'full':
      sync.runSync().catch(error => {
        console.error('Sync failed:', error);
        process.exit(1);
      });
      break;
      
    case 'role':
      if (!param) {
        console.error('Role ID required');
        process.exit(1);
      }
      sync.syncRole(param).catch(error => {
        console.error('Role sync failed:', error);
        process.exit(1);
      });
      break;
      
    case 'users':
      sync.syncUserApiKeys(param).catch(error => {
        console.error('User sync failed:', error);
        process.exit(1);
      });
      break;
      
    case 'validate':
      sync.validateSync().catch(error => {
        console.error('Validation failed:', error);
        process.exit(1);
      });
      break;
      
    default:
      console.log('Usage:');
      console.log('  node scripts/sync-tyk-policies.js full              - Full sync');
      console.log('  node scripts/sync-tyk-policies.js role <role-id>    - Sync specific role');
      console.log('  node scripts/sync-tyk-policies.js users [user-id]   - Sync user API keys');
      console.log('  node scripts/sync-tyk-policies.js validate          - Validate sync status');
      process.exit(1);
  }
}

module.exports = TykPolicySync;
