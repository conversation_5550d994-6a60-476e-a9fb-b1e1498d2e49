const passport = require('../config/passport');
const { status: httpStatus } = require("http-status");
const { ApiError } = require("../helpers/api.helper");
const config = require('../config/config');
const tykAuth = require('./tykAuth');
const logger = require('../config/logger');

// The verifyCallback now uses permissions directly from the JWT token.
const verifyCallback = (req, resolve, reject, requiredRights) => async (err, identity, info) => {
  if (err || info || !identity) {
    return reject(
      new ApiError(httpStatus.UNAUTHORIZED, "Please authenticate")
    );
  }

  req.identity = identity;

  if (requiredRights.length) {
    // Get permissions directly from the JWT token (added by passport strategy)
    const identityRights = identity.permissions || [];

    if (identityRights.length === 0) {
      return reject(
        new ApiError(httpStatus.FORBIDDEN, "No permissions assigned to identity")
      );
    }

    // Check required rights
    const hasRequiredRights = requiredRights.every((requiredRight) =>
      identityRights.includes(requiredRight)
    );

    if (
      !hasRequiredRights &&
      req.params.identityId !== identity.identity_id
    ) {
      return reject(new ApiError(httpStatus.FORBIDDEN, "Unauthorized access of identity"));
    }
  }

  resolve();
};

const auth = (...requiredRights) =>
  async (req, res, next) => {
    // Check authentication mode and route accordingly
    if (config.auth.mode === 'tyk') {
      logger.debug('Using Tyk authentication mode');
      return tykAuth(...requiredRights)(req, res, next);
    } else {
      logger.debug('Using custom authentication mode');
      return new Promise((resolve, reject) => {
        passport.authenticate(
          "custom",
          { session: false },
          verifyCallback(req, resolve, reject, requiredRights)
        )(req, res, next);
      })
        .then(() => next())
        .catch((err) => next(err));
    }
  };

module.exports = auth;
