#!/usr/bin/env node

/**
 * Tyk Integration Test Script
 * Tests both custom and Tyk authentication modes
 */

const axios = require('axios');
const config = require('../config/config');
const tykService = require('../services/tyk.service');
const tykPolicyManager = require('../utilities/tykPolicyManager');
const logger = require('../config/logger');

const API_BASE_URL = `http://localhost:${config.port}`;

class TykIntegrationTester {
  constructor() {
    this.testResults = [];
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting Tyk Integration Tests...\n');

    try {
      // Test configuration
      await this.testConfiguration();

      // Test authentication modes
      await this.testCustomAuth();
      
      if (config.auth.mode === 'tyk') {
        await this.testTykAuth();
        await this.testTykServices();
        await this.testPolicyManager();
      }

      // Test rate limiting
      await this.testRateLimiting();

      // Print results
      this.printResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Test configuration
   */
  async testConfiguration() {
    console.log('📋 Testing Configuration...');

    try {
      // Test auth mode
      const authMode = config.auth.mode;
      this.addResult('Configuration', 'Auth Mode', authMode === 'custom' || authMode === 'tyk', authMode);

      // Test required configs
      if (authMode === 'tyk') {
        const tykConfig = require('../config/tyk');
        this.addResult('Configuration', 'Tyk Enabled', tykConfig.isEnabled, tykConfig.isEnabled);
        this.addResult('Configuration', 'Tyk Gateway URL', !!tykConfig.gatewayUrl, tykConfig.gatewayUrl);
        this.addResult('Configuration', 'Tyk Dashboard URL', !!tykConfig.dashboardUrl, tykConfig.dashboardUrl);
      }

      console.log('✅ Configuration tests completed\n');
    } catch (error) {
      console.error('❌ Configuration test failed:', error.message);
      this.addResult('Configuration', 'Error', false, error.message);
    }
  }

  /**
   * Test custom authentication
   */
  async testCustomAuth() {
    console.log('🔐 Testing Custom Authentication...');

    try {
      // Test health endpoint (no auth required)
      const healthResponse = await axios.get(`${API_BASE_URL}/health`);
      this.addResult('Custom Auth', 'Health Endpoint', healthResponse.status === 200, healthResponse.status);

      // Test login endpoint
      try {
        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: '<EMAIL>',
          password: 'Pa$$w0rd!'
        });
        this.addResult('Custom Auth', 'Login', loginResponse.status === 200, 'Success');
        
        if (loginResponse.data.tokens) {
          this.addResult('Custom Auth', 'Token Generation', true, 'Tokens received');
        }
      } catch (error) {
        this.addResult('Custom Auth', 'Login', false, error.response?.status || error.message);
      }

      console.log('✅ Custom authentication tests completed\n');
    } catch (error) {
      console.error('❌ Custom authentication test failed:', error.message);
      this.addResult('Custom Auth', 'Error', false, error.message);
    }
  }

  /**
   * Test Tyk authentication
   */
  async testTykAuth() {
    console.log('🔒 Testing Tyk Authentication...');

    try {
      // Test Tyk Gateway health
      const gatewayHealth = await tykService.checkGatewayHealth();
      this.addResult('Tyk Auth', 'Gateway Health', gatewayHealth, gatewayHealth ? 'Online' : 'Offline');

      // Test Tyk Dashboard health
      const dashboardHealth = await tykService.checkDashboardHealth();
      this.addResult('Tyk Auth', 'Dashboard Health', dashboardHealth, dashboardHealth ? 'Online' : 'Offline');

      // Test JWT generation
      const mockIdentity = {
        identity_id: 'test-identity',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User'
      };
      const tykToken = tykService.generateTykJWT(mockIdentity, ['test_permission']);
      this.addResult('Tyk Auth', 'JWT Generation', !!tykToken, 'Token generated');

      // Test JWT validation
      const validatedToken = await tykService.validateJWT(tykToken);
      this.addResult('Tyk Auth', 'JWT Validation', !!validatedToken, 'Token validated');

      console.log('✅ Tyk authentication tests completed\n');
    } catch (error) {
      console.error('❌ Tyk authentication test failed:', error.message);
      this.addResult('Tyk Auth', 'Error', false, error.message);
    }
  }

  /**
   * Test Tyk services
   */
  async testTykServices() {
    console.log('⚙️ Testing Tyk Services...');

    try {
      // Test API creation (mock)
      try {
        const apiResult = await tykService.createOrUpdateApi('test-api', 'test-api-id');
        this.addResult('Tyk Services', 'API Creation', !!apiResult, 'API created');
      } catch (error) {
        this.addResult('Tyk Services', 'API Creation', false, error.message);
      }

      // Test policy creation (mock)
      try {
        const policyResult = await tykService.createPolicy('test-policy', ['test_permission']);
        this.addResult('Tyk Services', 'Policy Creation', !!policyResult, 'Policy created');
      } catch (error) {
        this.addResult('Tyk Services', 'Policy Creation', false, error.message);
      }

      console.log('✅ Tyk services tests completed\n');
    } catch (error) {
      console.error('❌ Tyk services test failed:', error.message);
      this.addResult('Tyk Services', 'Error', false, error.message);
    }
  }

  /**
   * Test policy manager
   */
  async testPolicyManager() {
    console.log('📋 Testing Policy Manager...');

    try {
      // Test policy cache
      tykPolicyManager.clearCache();
      this.addResult('Policy Manager', 'Cache Clear', true, 'Cache cleared');

      // Test rate limit configuration
      const adminRateLimit = tykPolicyManager.getRateLimitForRole('admin');
      this.addResult('Policy Manager', 'Rate Limit Config', !!adminRateLimit, `${adminRateLimit.rate}/${adminRateLimit.per}s`);

      // Test quota configuration
      const adminQuota = tykPolicyManager.getQuotaForRole('admin');
      this.addResult('Policy Manager', 'Quota Config', !!adminQuota, `Max: ${adminQuota.max}`);

      console.log('✅ Policy manager tests completed\n');
    } catch (error) {
      console.error('❌ Policy manager test failed:', error.message);
      this.addResult('Policy Manager', 'Error', false, error.message);
    }
  }

  /**
   * Test rate limiting
   */
  async testRateLimiting() {
    console.log('🚦 Testing Rate Limiting...');

    try {
      // Test multiple requests to health endpoint
      const requests = [];
      for (let i = 0; i < 5; i++) {
        requests.push(axios.get(`${API_BASE_URL}/health`));
      }

      const responses = await Promise.allSettled(requests);
      const successCount = responses.filter(r => r.status === 'fulfilled').length;
      
      this.addResult('Rate Limiting', 'Multiple Requests', successCount > 0, `${successCount}/5 successful`);

      console.log('✅ Rate limiting tests completed\n');
    } catch (error) {
      console.error('❌ Rate limiting test failed:', error.message);
      this.addResult('Rate Limiting', 'Error', false, error.message);
    }
  }

  /**
   * Add test result
   */
  addResult(category, test, passed, details) {
    this.testResults.push({
      category,
      test,
      passed,
      details
    });
  }

  /**
   * Print test results
   */
  printResults() {
    console.log('📊 Test Results Summary:');
    console.log('=' * 50);

    const categories = [...new Set(this.testResults.map(r => r.category))];
    
    categories.forEach(category => {
      console.log(`\n${category}:`);
      const categoryResults = this.testResults.filter(r => r.category === category);
      
      categoryResults.forEach(result => {
        const status = result.passed ? '✅' : '❌';
        console.log(`  ${status} ${result.test}: ${result.details}`);
      });
    });

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n' + '=' * 50);
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Some tests failed. Check the configuration and Tyk setup.');
      process.exit(1);
    } else {
      console.log('\n🎉 All tests passed! Tyk integration is working correctly.');
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new TykIntegrationTester();
  tester.runAllTests().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = TykIntegrationTester;
