# Tyk Integration for CareMate API

This document describes the comprehensive Tyk integration that has been implemented for the CareMate API, providing both custom and Tyk-based authentication, authorization, rate limiting, and SSO capabilities.

## Overview

The integration supports two authentication modes:
- **Custom Mode** (default): Uses the existing passport-based authentication system
- **Tyk Mode**: Uses Tyk Gateway for authentication, authorization, rate limiting, and SSO

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```bash
# Authentication Mode
AUTH=tyk  # or 'custom' for existing system

# Tyk Configuration
TYK_GATEWAY_URL=http://localhost:8080
TYK_DASHBOARD_URL=http://localhost:3000
TYK_SECRET=your-tyk-gateway-secret
TYK_API_KEY=your-tyk-dashboard-api-key
TYK_ORG_ID=your-tyk-organization-id
TYK_LISTEN_PATH=/api/v1
TYK_TARGET_URL=http://localhost:3001
```

See `.env.tyk.example` for a complete configuration template.

## Features

### 1. Dual Authentication System

The system automatically switches between custom and Tyk authentication based on the `AUTH` environment variable:

- **Custom Mode**: Uses existing JWT tokens and passport strategies
- **Tyk Mode**: Uses Tyk Gateway for token validation and policy enforcement

### 2. Dynamic Role and Permission Management

The Tyk integration maintains compatibility with your existing dynamic role system:

- Roles and permissions are automatically synced to Tyk policies
- Dynamic role creation/updates trigger corresponding Tyk policy updates
- Permissions are enforced at the Tyk Gateway level

### 3. Rate Limiting Integration

- **Custom Mode**: Uses express-rate-limit with configurable limits
- **Tyk Mode**: Leverages Tyk's advanced rate limiting with per-user quotas
- Automatic fallback to custom rate limiting if Tyk is unavailable

### 4. SSO Integration

Tyk mode replaces passport strategies with Tyk's built-in SSO capabilities:

- **SAML**: Enterprise SSO integration
- **OAuth 2.0**: Third-party OAuth providers
- **OpenID Connect**: Modern identity providers
- **Azure AD**: Microsoft Azure Active Directory

### 5. Dual Logging System

- Logs are sent to both local winston logger and Tyk Analytics
- Request/response logging includes authentication context
- Error tracking with Tyk integration status

## File Structure

### Core Configuration
- `config/tyk.js` - Main Tyk configuration and API definition generation
- `config/tykSSO.js` - SSO provider integration for Tyk
- `config/config.js` - Updated with Tyk environment variables

### Middleware
- `middlewares/tykAuth.js` - Tyk authentication middleware
- `middlewares/auth.js` - Updated to support both modes
- `middlewares/rateLimiter.js` - Dual rate limiting implementation

### Services
- `services/tyk.service.js` - Tyk API interactions and token management
- `utilities/tykPolicyManager.js` - Dynamic policy management

### Updated Files
- `controllers/auth.controller.js` - Support for both authentication modes
- `config/morgan.js` - Enhanced logging with Tyk analytics
- `config/logger.js` - Dual logging transport
- `app.js` - Conditional initialization based on auth mode

## Usage

### Starting the Application

1. **Custom Mode** (default):
   ```bash
   AUTH=custom npm start
   ```

2. **Tyk Mode**:
   ```bash
   AUTH=tyk npm start
   ```

### API Authentication

#### Custom Mode
```bash
# Login to get JWT token
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Use token in subsequent requests
curl -X GET http://localhost:3000/facility \
  -H "Authorization: Bearer <jwt-token>"
```

#### Tyk Mode
```bash
# Login to get Tyk token
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Use Tyk token in subsequent requests
curl -X GET http://localhost:3000/facility \
  -H "Authorization: Bearer <tyk-token>"
```

### SSO Authentication

#### Custom Mode (Passport)
```bash
# SAML
GET /auth/saml/login

# OIDC  
GET /auth/oidc/login

# Azure AD
GET /auth/azure/login
```

#### Tyk Mode (Tyk Gateway)
```bash
# SAML (handled by Tyk)
GET /auth/saml/

# OIDC (handled by Tyk)
GET /auth/oidc/

# Azure AD (handled by Tyk)
GET /auth/azure/
```

## Dynamic Role Management

### Creating Roles with Tyk Integration

When `AUTH=tyk`, creating or updating roles automatically:

1. Creates corresponding Tyk policies
2. Sets appropriate rate limits based on role
3. Configures quotas and access rights
4. Updates existing user API keys

### Permission Enforcement

Permissions are enforced at multiple levels:

1. **Application Level**: Middleware checks user permissions
2. **Tyk Level**: Gateway enforces policy-based access
3. **Database Level**: Role-based data access

## Monitoring and Analytics

### Tyk Analytics

When in Tyk mode, the following data is sent to Tyk Analytics:

- Request/response metrics
- Authentication events
- Rate limiting violations
- Error tracking
- User activity patterns

### Local Logging

All events are also logged locally with enhanced context:

- Authentication mode
- User identity
- Request details
- Performance metrics

## Troubleshooting

### Common Issues

1. **Tyk Connection Failed**
   - Check `TYK_GATEWAY_URL` and `TYK_DASHBOARD_URL`
   - Verify Tyk services are running
   - Check network connectivity

2. **Authentication Failures**
   - Verify `TYK_SECRET` and `TYK_API_KEY`
   - Check token expiration settings
   - Review Tyk policy configurations

3. **SSO Issues**
   - Verify SSO provider configurations
   - Check callback URLs
   - Review Tyk SSO middleware setup

### Fallback Behavior

The system is designed to gracefully handle Tyk unavailability:

- Authentication falls back to custom mode
- Rate limiting uses express-rate-limit
- Logging continues locally
- SSO falls back to passport strategies

## Migration Guide

### From Custom to Tyk

1. Set up Tyk Gateway and Dashboard
2. Configure environment variables
3. Set `AUTH=tyk`
4. Run role sync: `POST /admin/sync-roles-to-tyk`
5. Test authentication flows
6. Monitor logs for any issues

### From Tyk to Custom

1. Set `AUTH=custom`
2. Restart application
3. Verify passport strategies are working
4. Check custom rate limiting
5. Test SSO flows

## Security Considerations

- Tyk tokens are stateless and include permissions
- Rate limiting is enforced at the gateway level
- All authentication events are logged
- SSO integrations use industry standards
- Fallback mechanisms maintain security

## Performance Impact

- Tyk mode adds gateway overhead but improves scalability
- Local caching reduces Tyk API calls
- Async logging prevents request blocking
- Policy caching improves response times

## Support

For issues related to:
- **Tyk Integration**: Check Tyk documentation and logs
- **Custom Authentication**: Review passport configurations
- **SSO Issues**: Verify provider settings
- **Performance**: Monitor both local and Tyk analytics
