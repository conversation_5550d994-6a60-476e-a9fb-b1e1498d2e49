const tykService = require('../services/tyk.service');
const tykConfig = require('../config/tyk');
const logger = require('../config/logger');
const { Role, Permission, RolePermission, Identity, IdentityRole } = require('../models');

/**
 * Tyk Policy Manager
 * Manages dynamic creation and updates of Tyk policies based on roles and permissions
 */

class TykPolicyManager {
  constructor() {
    this.policyCache = new Map(); // Cache for policy mappings
    this.isEnabled = tykConfig.isEnabled;
  }

  /**
   * Create or update Tyk policy for a role
   */
  async createRolePolicy(role, permissions = null) {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping policy creation');
      return null;
    }

    try {
      // Get permissions if not provided
      if (!permissions) {
        permissions = await this.getRolePermissions(role.role_id);
      }

      const policyName = `role_${role.name}_${role.role_id}`;
      const permissionNames = permissions.map(p => p.name);

      // Define rate limiting based on role
      const rateLimit = this.getRateLimitForRole(role.name);
      const quota = this.getQuotaForRole(role.name);

      // Create policy in Tyk
      const policyResult = await tykService.createPolicy(
        policyName,
        permissionNames,
        rateLimit,
        quota
      );

      // Cache the policy mapping
      this.policyCache.set(role.role_id, {
        policyId: policyResult.id || policyResult._id,
        policyName: policyName,
        permissions: permissionNames,
        lastUpdated: new Date()
      });

      logger.info(`Tyk policy created for role: ${role.name} (${role.role_id})`);
      return policyResult;

    } catch (error) {
      logger.error(`Failed to create Tyk policy for role ${role.name}:`, error.message);
      throw error;
    }
  }

  /**
   * Update Tyk policy when role permissions change
   */
  async updateRolePolicy(role, newPermissions) {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping policy update');
      return null;
    }

    try {
      const cachedPolicy = this.policyCache.get(role.role_id);
      
      if (!cachedPolicy) {
        // Policy doesn't exist, create it
        return await this.createRolePolicy(role, newPermissions);
      }

      const policyName = `role_${role.name}_${role.role_id}`;
      const permissionNames = newPermissions.map(p => p.name);

      // Check if permissions have changed
      const currentPermissions = cachedPolicy.permissions.sort();
      const newPermissionsSorted = permissionNames.sort();
      
      if (JSON.stringify(currentPermissions) === JSON.stringify(newPermissionsSorted)) {
        logger.debug(`No permission changes for role ${role.name}, skipping update`);
        return cachedPolicy;
      }

      // Update rate limiting and quota
      const rateLimit = this.getRateLimitForRole(role.name);
      const quota = this.getQuotaForRole(role.name);

      // Update policy in Tyk
      const policyResult = await tykService.updatePolicy(
        cachedPolicy.policyId,
        policyName,
        permissionNames,
        rateLimit,
        quota
      );

      // Update cache
      this.policyCache.set(role.role_id, {
        policyId: cachedPolicy.policyId,
        policyName: policyName,
        permissions: permissionNames,
        lastUpdated: new Date()
      });

      logger.info(`Tyk policy updated for role: ${role.name} (${role.role_id})`);
      return policyResult;

    } catch (error) {
      logger.error(`Failed to update Tyk policy for role ${role.name}:`, error.message);
      throw error;
    }
  }

  /**
   * Delete Tyk policy for a role
   */
  async deleteRolePolicy(roleId) {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping policy deletion');
      return null;
    }

    try {
      const cachedPolicy = this.policyCache.get(roleId);
      
      if (!cachedPolicy) {
        logger.warn(`No cached policy found for role ID: ${roleId}`);
        return null;
      }

      // Delete policy from Tyk
      await tykService.deletePolicy(cachedPolicy.policyId);

      // Remove from cache
      this.policyCache.delete(roleId);

      logger.info(`Tyk policy deleted for role ID: ${roleId}`);
      return true;

    } catch (error) {
      logger.error(`Failed to delete Tyk policy for role ID ${roleId}:`, error.message);
      throw error;
    }
  }

  /**
   * Create or update Tyk API key for an identity
   */
  async createIdentityApiKey(identity, roles = null) {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping API key creation');
      return null;
    }

    try {
      // Get roles if not provided
      if (!roles) {
        roles = await this.getIdentityRoles(identity.identity_id);
      }

      // Get policy IDs for the identity's roles
      const policyIds = [];
      for (const role of roles) {
        const cachedPolicy = this.policyCache.get(role.role_id);
        if (cachedPolicy) {
          policyIds.push(cachedPolicy.policyId);
        } else {
          // Create policy if it doesn't exist
          const permissions = await this.getRolePermissions(role.role_id);
          const policyResult = await this.createRolePolicy(role, permissions);
          if (policyResult && (policyResult.id || policyResult._id)) {
            policyIds.push(policyResult.id || policyResult._id);
          }
        }
      }

      // Create API key with policies
      const keyData = {
        allowance: 10000, // Default allowance
        rate: 1000, // Requests per minute
        per: 60, // Time window in seconds
        quotaMax: -1, // Unlimited quota
        metaData: {
          identity_id: identity.identity_id,
          email: identity.email,
          first_name: identity.first_name,
          last_name: identity.last_name,
          roles: roles.map(r => r.name)
        }
      };

      const apiKeyResult = await tykService.createApiKey(
        identity.identity_id,
        policyIds,
        keyData
      );

      logger.info(`Tyk API key created for identity: ${identity.identity_id}`);
      return apiKeyResult;

    } catch (error) {
      logger.error(`Failed to create Tyk API key for identity ${identity.identity_id}:`, error.message);
      throw error;
    }
  }

  /**
   * Sync all roles and permissions to Tyk policies
   */
  async syncAllRolesToTyk() {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping role sync');
      return;
    }

    try {
      logger.info('Starting sync of all roles to Tyk policies...');

      // Get all active roles
      const roles = await Role.findAll({
        where: { is_active: true },
        include: [
          {
            model: Permission,
            as: 'permission',
            through: { attributes: [] }
          }
        ]
      });

      const syncResults = [];

      for (const role of roles) {
        try {
          const permissions = role.permission || [];
          const result = await this.createRolePolicy(role, permissions);
          syncResults.push({
            roleId: role.role_id,
            roleName: role.name,
            success: true,
            policyId: result?.id || result?._id
          });
        } catch (error) {
          syncResults.push({
            roleId: role.role_id,
            roleName: role.name,
            success: false,
            error: error.message
          });
        }
      }

      const successCount = syncResults.filter(r => r.success).length;
      const failureCount = syncResults.filter(r => !r.success).length;

      logger.info(`Role sync completed: ${successCount} successful, ${failureCount} failed`);
      
      if (failureCount > 0) {
        logger.warn('Failed role syncs:', syncResults.filter(r => !r.success));
      }

      return syncResults;

    } catch (error) {
      logger.error('Failed to sync roles to Tyk:', error.message);
      throw error;
    }
  }

  /**
   * Get permissions for a role
   */
  async getRolePermissions(roleId) {
    try {
      const rolePermissions = await RolePermission.findAll({
        where: { role_id: roleId },
        include: [
          {
            model: Permission,
            as: 'permission',
            attributes: ['permission_id', 'name', 'description']
          }
        ]
      });

      return rolePermissions.map(rp => rp.permission);
    } catch (error) {
      logger.error(`Failed to get permissions for role ${roleId}:`, error.message);
      return [];
    }
  }

  /**
   * Get roles for an identity
   */
  async getIdentityRoles(identityId) {
    try {
      const identityRoles = await IdentityRole.findAll({
        where: { identity_id: identityId },
        include: [
          {
            model: Role,
            as: 'role',
            attributes: ['role_id', 'name', 'description']
          }
        ]
      });

      return identityRoles.map(ir => ir.role);
    } catch (error) {
      logger.error(`Failed to get roles for identity ${identityId}:`, error.message);
      return [];
    }
  }

  /**
   * Get rate limit configuration for a role
   */
  getRateLimitForRole(roleName) {
    const rateLimits = {
      'admin': { rate: 10000, per: 60 },
      'kiosk': { rate: 1000, per: 60 },
      'user': { rate: 5000, per: 60 },
      'guest': { rate: 100, per: 60 }
    };

    return rateLimits[roleName.toLowerCase()] || { rate: 1000, per: 60 };
  }

  /**
   * Get quota configuration for a role
   */
  getQuotaForRole(roleName) {
    const quotas = {
      'admin': { max: -1, renews: 0, remaining: -1, renewalRate: 0 },
      'kiosk': { max: 100000, renews: Date.now() + 86400000, remaining: 100000, renewalRate: 86400 },
      'user': { max: 50000, renews: Date.now() + 86400000, remaining: 50000, renewalRate: 86400 },
      'guest': { max: 1000, renews: Date.now() + 86400000, remaining: 1000, renewalRate: 86400 }
    };

    return quotas[roleName.toLowerCase()] || { max: 10000, renews: Date.now() + 86400000, remaining: 10000, renewalRate: 86400 };
  }

  /**
   * Clear policy cache
   */
  clearCache() {
    this.policyCache.clear();
    logger.info('Tyk policy cache cleared');
  }

  /**
   * Get cached policy for a role
   */
  getCachedPolicy(roleId) {
    return this.policyCache.get(roleId);
  }
}

module.exports = new TykPolicyManager();
