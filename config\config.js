const dotenv = require("dotenv");
const path = require("path");
const Joi = require("joi");

// Determine the env file path dynamically
let envFilePath = null;
const envFileArgIndex = process.argv.indexOf("--env-file");
if (envFileArgIndex !== -1 && process.argv[envFileArgIndex + 1])
  envFilePath = process.argv[envFileArgIndex + 1];
if (!envFilePath && process.env.ENV_FILE) envFilePath = process.env.ENV_FILE;

switch (process.env?.NODE_ENV) {
  case "local":
    envFilePath = ".env.local";
    break;
  case "development":
    envFilePath = ".env.dev";
    break;
  case "production":
    envFilePath = ".env";
    break;
  default:
    envFilePath = ".env.local";
}

// Resolve the full path to the env file (assuming your .env files are two levels up)
const fullEnvPath = path.join(process.cwd(), envFilePath);
dotenv.config({ path: fullEnvPath });

const envVarsSchema = Joi.object()
  .keys({
    NODE_ENV: Joi.string()
      .valid("production", "development", "local")
      .required(),
    PORT: Joi.number().default(3000),

    DB_DIALECT: Joi.string()
      .required()
      .description("Database dialect (e.g., mysql, postgres)"),
    DB_WRITE_HOST: Joi.string().required().description("Database write host"),
    DB_WRITE_USERNAME: Joi.string()
      .required()
      .description("Database write username"),
    DB_WRITE_PASSWORD: Joi.string()
      .required()
      .description("Database write password"),
    DB_WRITE_DATABASE: Joi.string()
      .required()
      .description("Database write database"),
    DB_READ_HOST: Joi.string().required().description("Database read host"),
    DB_READ_USERNAME: Joi.string()
      .required()
      .description("Database read username"),
    DB_READ_PASSWORD: Joi.string()
      .required()
      .description("Database read password"),
    DB_READ_DATABASE: Joi.string()
      .required()
      .description("Database read database"),
    DB_LOGGING: Joi.boolean()
      .default(false)
      .description("Enable or disable database logging"),

    MESSAGE_QUEUING: Joi.boolean()
      .default(true)
      .description("Enable or disable message queuing"),
    RABBITMQ_URL: Joi.string().required().description("RabbitMQ url"),
    CONCURRENCY_LIMIT: Joi.number()
      .integer()
      .min(1)
      .default(15)
      .description("Maximum number of concurrent message processing (0 for unlimited)"),
    COLLECTION_TIMEOUT: Joi.number()
      .integer()
      .min(100)
      .default(3000)
      .description("Maximum time in milliseconds to wait for collecting messages before processing"),
    SHUTDOWN_TIMEOUT: Joi.number()
      .integer()
      .min(1000)
      .default(30000)
      .description("Maximum time in milliseconds to wait for active processing to complete during shutdown"),

    JWT_SECRET: Joi.string().required().description("JWT secret key"),
    JWT_ACCESS_EXPIRATION_MINUTES: Joi.number()
      .default(30)
      .description("minutes after which access tokens expire"),
    JWT_REFRESH_EXPIRATION_DAYS: Joi.number()
      .default(30)
      .description("days after which refresh tokens expire"),

    LOG_LEVEL: Joi.string().default("info").description("Log level"),
    LOG_MAX_SIZE: Joi.string()
      .default("20m")
      .description("Maximum log file size before rotation"),
    LOG_MAX_FILES: Joi.string()
      .default("30d")
      .description("Maximum number of log files to keep"),
    LOG_FILE_TRANSPORT: Joi.boolean()
      .default(false)
      .description("Enable file transport for logs"),

    // Authentication Configuration
    AUTH: Joi.string()
      .valid("custom", "tyk")
      .default("custom")
      .description("Authentication mode: custom or tyk"),

    // Tyk Configuration
    TYK_GATEWAY_URL: Joi.string()
      .when("AUTH", {
        is: "tyk",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .description("Tyk Gateway URL"),
    TYK_DASHBOARD_URL: Joi.string()
      .when("AUTH", {
        is: "tyk",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .description("Tyk Dashboard URL"),
    TYK_SECRET: Joi.string()
      .when("AUTH", {
        is: "tyk",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .description("Tyk Gateway Secret"),
    TYK_API_KEY: Joi.string()
      .when("AUTH", {
        is: "tyk",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .description("Tyk Dashboard API Key"),
    TYK_ORG_ID: Joi.string()
      .when("AUTH", {
        is: "tyk",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .description("Tyk Organization ID"),
    TYK_LISTEN_PATH: Joi.string()
      .default("/api/v1")
      .description("Tyk API Listen Path"),
    TYK_TARGET_URL: Joi.string()
      .when("AUTH", {
        is: "tyk",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .description("Target URL for Tyk to proxy to"),
    TYK_CALLBACK_BASE_URL: Joi.string()
      .when("AUTH", {
        is: "tyk",
        then: Joi.required(),
        otherwise: Joi.optional(),
      })
      .description("Base URL for SSO callbacks"),

    CACHE_DRIVER: Joi.string()
      .valid("redis", "memcached", "memory")
      .default("memory")
      .description("Caching driver (redis, memcached, or memory)"),
    CACHE_TTL: Joi.number()
      .default(60)
      .description("Time-to-live in seconds for cache"),
    REDIS_HOST: Joi.string().default("localhost").description("Redis host"),
    REDIS_PORT: Joi.number().default(6379).description("Redis port"),
    MEMCACHED_HOST: Joi.string()
      .default("localhost:11211")
      .description("Memcached host"),

    EMAIL_HOST: Joi.string().required().description("Email service host"),
    EMAIL_PORT: Joi.number().integer().min(1).max(65535).required().description("Email service port"),
    EMAIL_USER: Joi.string().required().description("Email service user"),
    EMAIL_PASS: Joi.string().required().description("Email service password"),
    TELNYX_API_KEY: Joi.string().required().description("Telnyx API key required"),
    TELNYX_PHONE_NUMBER: Joi.string().required().description("Telnyx phone number required"),
  })
  .unknown();

const { value: envVars, error } = envVarsSchema
  .prefs({ errors: { label: "key" } })
  .validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}
module.exports = {
  env: envVars.NODE_ENV,
  server_url: envVars.SERVER_URL,
  port: envVars.PORT,
  database: {
    dialect: envVars.DB_DIALECT,
    write: {
      host: envVars.DB_WRITE_HOST,
      username: envVars.DB_WRITE_USERNAME,
      password: envVars.DB_WRITE_PASSWORD,
      database: envVars.DB_WRITE_DATABASE,
    },
    read: {
      host: envVars.DB_READ_HOST,
      username: envVars.DB_READ_USERNAME,
      password: envVars.DB_READ_PASSWORD,
      database: envVars.DB_READ_DATABASE,
    },
    logging: envVars.DB_LOGGING,
  },
  jwt: {
    secret: envVars.JWT_SECRET,
    accessExpirationMinutes: envVars.JWT_ACCESS_EXPIRATION_MINUTES,
    refreshExpirationDays: envVars.JWT_REFRESH_EXPIRATION_DAYS,
  },
  logger: {
    level: envVars.LOG_LEVEL,
    maxSize: envVars.LOG_MAX_SIZE,
    maxFiles: envVars.LOG_MAX_FILES,
    fileTransport: envVars.LOG_FILE_TRANSPORT,
  },
  saml: {
    entryPoint: envVars.SAML_ENTRY_POINT,
    issuer: envVars.SAML_ISSUER,
    cert: envVars.SAML_CERT,
    callbackUrl: envVars.SAML_CALLBACK_URL,
  },
  oidc: {
    issuer: envVars.OIDC_ISSUER,
    authorizationURL: envVars.OIDC_AUTHORIZATION_URL,
    tokenURL: envVars.OIDC_TOKEN_URL,
    userInfoURL: envVars.OIDC_USERINFO_URL,
    clientID: envVars.OIDC_CLIENT_ID,
    clientSecret: envVars.OIDC_CLIENT_SECRET,
    callbackURL: envVars.OIDC_CALLBACK_URL,
  },
  azure: {
    tenantID: envVars.AZURE_TENANT_ID,
    clientID: envVars.AZURE_CLIENT_ID,
    clientSecret: envVars.AZURE_CLIENT_SECRET,
    callbackURL: envVars.AZURE_CALLBACK_URL,
    identityMetadata: `https://login.microsoftonline.com/${envVars.AZURE_TENANT_ID}/v2.0/.well-known/openid-configuration`,
    responseType: "id_token",
    responseMode: "form_post",
    allowHttpForRedirectUrl: true,
  },
  messageQueuing: {
    status: envVars.MESSAGE_QUEUING,
    rabitmq_url: envVars.RABBITMQ_URL,
  },
  messageProcessing: {
    concurrencyLimit: envVars.CONCURRENCY_LIMIT,
    collectionTimeout: envVars.COLLECTION_TIMEOUT,
    shutdownTimeout: envVars.SHUTDOWN_TIMEOUT,
  },
  caching: {
    driver: envVars.CACHE_DRIVER,
    ttl: parseInt(envVars.CACHE_TTL, 10),
    redis: {
      host: envVars.REDIS_HOST,
      port: envVars.REDIS_PORT,
    },
    memcached: {
      host: envVars.MEMCACHED_HOST,
    },
  },
  email: {
    host: envVars.EMAIL_HOST,
    port: envVars.EMAIL_PORT,
    user: envVars.EMAIL_USER,
    pass: envVars.EMAIL_PASS,
  },
  telnyx: {
    TELNYX_API_KEY: envVars.TELNYX_API_KEY,
    TELNYX_PHONE_NUMBER: envVars.TELNYX_PHONE_NUMBER,
  },
  auth: {
    mode: envVars.AUTH,
  },
  tyk: {
    gatewayUrl: envVars.TYK_GATEWAY_URL,
    dashboardUrl: envVars.TYK_DASHBOARD_URL,
    secret: envVars.TYK_SECRET,
    apiKey: envVars.TYK_API_KEY,
    orgId: envVars.TYK_ORG_ID,
    listenPath: envVars.TYK_LISTEN_PATH,
    targetUrl: envVars.TYK_TARGET_URL,
    callbackBaseUrl: envVars.TYK_CALLBACK_BASE_URL,
  },
};
