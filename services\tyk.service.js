const axios = require('axios');
const jwt = require('jsonwebtoken');
const moment = require('moment');
const tykConfig = require('../config/tyk');
const config = require('../config/config');
const logger = require('../config/logger');
const { v4: uuidv4 } = require('uuid');
const { withTykErrorHandling, withTykRetry, tykCircuitBreakers } = require('../helpers/tykError.helper');

/**
 * Tyk Service Layer
 * Handles all interactions with Tyk Gateway and Dashboard APIs
 */

class TykService {
  constructor() {
    this.gatewayUrl = tykConfig.gatewayUrl;
    this.dashboardUrl = tykConfig.dashboardUrl;
    this.orgId = tykConfig.orgId;
  }

  /**
   * Create or update API definition in Tyk
   */
  async createOrUpdateApi(apiName, apiId = null) {
    const serviceCall = withTykRetry(
      withTykErrorHandling(async () => {
        const apiDefinition = tykConfig.generateApiDefinition(apiName, apiId || uuidv4());

        const response = await axios.post(
          `${this.dashboardUrl}/api/apis`,
          apiDefinition,
          {
            headers: tykConfig.getDashboardHeaders(),
            timeout: 10000
          }
        );

        logger.info(`API definition created/updated: ${apiName}`);
        return response.data;
      }, { service: 'dashboard', operation: 'createOrUpdateApi' }),
      {
        maxRetries: 3,
        context: { service: 'dashboard', operation: 'createOrUpdateApi' }
      }
    );

    return await tykCircuitBreakers.dashboard.call(serviceCall, { service: 'dashboard', operation: 'createOrUpdateApi' });
  }

  /**
   * Create policy in Tyk Dashboard
   */
  async createPolicy(policyName, permissions = [], rateLimit = null, quota = null) {
    try {
      const policyDefinition = tykConfig.generatePolicyDefinition(
        policyName, 
        permissions, 
        rateLimit, 
        quota
      );

      const response = await axios.post(
        `${this.dashboardUrl}/api/portal/policies`,
        policyDefinition,
        {
          headers: tykConfig.getDashboardHeaders(),
          timeout: 10000
        }
      );

      logger.info(`Policy created: ${policyName}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to create policy:', error.message);
      throw new Error(`Tyk policy creation failed: ${error.message}`);
    }
  }

  /**
   * Update existing policy in Tyk Dashboard
   */
  async updatePolicy(policyId, policyName, permissions = [], rateLimit = null, quota = null) {
    try {
      const policyDefinition = tykConfig.generatePolicyDefinition(
        policyName, 
        permissions, 
        rateLimit, 
        quota
      );

      const response = await axios.put(
        `${this.dashboardUrl}/api/portal/policies/${policyId}`,
        policyDefinition,
        {
          headers: tykConfig.getDashboardHeaders(),
          timeout: 10000
        }
      );

      logger.info(`Policy updated: ${policyName} (${policyId})`);
      return response.data;
    } catch (error) {
      logger.error('Failed to update policy:', error.message);
      throw new Error(`Tyk policy update failed: ${error.message}`);
    }
  }

  /**
   * Delete policy from Tyk Dashboard
   */
  async deletePolicy(policyId) {
    try {
      await axios.delete(
        `${this.dashboardUrl}/api/portal/policies/${policyId}`,
        {
          headers: tykConfig.getDashboardHeaders(),
          timeout: 10000
        }
      );

      logger.info(`Policy deleted: ${policyId}`);
      return true;
    } catch (error) {
      logger.error('Failed to delete policy:', error.message);
      throw new Error(`Tyk policy deletion failed: ${error.message}`);
    }
  }

  /**
   * Create API key for user
   */
  async createApiKey(identityId, policies = [], keyData = {}) {
    try {
      const keyDefinition = {
        allowance: keyData.allowance || 1000,
        rate: keyData.rate || 1000,
        per: keyData.per || 60,
        expires: keyData.expires || 0,
        quota_max: keyData.quotaMax || -1,
        quota_renews: keyData.quotaRenews || moment().add(1, 'month').unix(),
        quota_remaining: keyData.quotaRemaining || -1,
        quota_renewal_rate: keyData.quotaRenewalRate || 2592000,
        org_id: this.orgId,
        meta_data: {
          identity_id: identityId,
          created_at: new Date().toISOString(),
          ...keyData.metaData
        },
        apply_policies: policies,
        hmac_enabled: false,
        hmac_string: "",
        is_inactive: false,
        apply_policy_id: "",
        data_expires: keyData.dataExpires || 0,
        monitor: {
          trigger_limits: []
        },
        enable_detailed_recording: false,
        tags: ["user-key"]
      };

      const response = await axios.post(
        `${this.dashboardUrl}/api/keys`,
        keyDefinition,
        {
          headers: tykConfig.getDashboardHeaders(),
          timeout: 10000
        }
      );

      logger.info(`API key created for identity: ${identityId}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to create API key:', error.message);
      throw new Error(`Tyk API key creation failed: ${error.message}`);
    }
  }

  /**
   * Update API key
   */
  async updateApiKey(keyId, keyData) {
    try {
      const response = await axios.put(
        `${this.dashboardUrl}/api/keys/${keyId}`,
        keyData,
        {
          headers: tykConfig.getDashboardHeaders(),
          timeout: 10000
        }
      );

      logger.info(`API key updated: ${keyId}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to update API key:', error.message);
      throw new Error(`Tyk API key update failed: ${error.message}`);
    }
  }

  /**
   * Delete API key
   */
  async deleteApiKey(keyId) {
    try {
      await axios.delete(
        `${this.dashboardUrl}/api/keys/${keyId}`,
        {
          headers: tykConfig.getDashboardHeaders(),
          timeout: 10000
        }
      );

      logger.info(`API key deleted: ${keyId}`);
      return true;
    } catch (error) {
      logger.error('Failed to delete API key:', error.message);
      throw new Error(`Tyk API key deletion failed: ${error.message}`);
    }
  }

  /**
   * Generate JWT token for Tyk authentication
   */
  generateTykJWT(identity, permissions = []) {
    const payload = {
      sub: identity.identity_id,
      iat: moment().unix(),
      exp: moment().add(config.jwt.accessExpirationMinutes, 'minutes').unix(),
      iss: 'caremate-api',
      aud: 'tyk-gateway',
      permissions: permissions,
      identity_type: identity.identity_type,
      facility_id: identity.facility_id,
      email: identity.email,
      first_name: identity.first_name,
      last_name: identity.last_name
    };

    return jwt.sign(payload, tykConfig.secret);
  }

  /**
   * Validate JWT token with Tyk
   */
  async validateJWT(token) {
    try {
      const decoded = jwt.verify(token, tykConfig.secret);
      return decoded;
    } catch (error) {
      logger.error('JWT validation failed:', error.message);
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Get API analytics from Tyk
   */
  async getApiAnalytics(apiId, from, to) {
    try {
      const response = await axios.get(
        `${this.dashboardUrl}/api/usage/apis/${apiId}`,
        {
          headers: tykConfig.getDashboardHeaders(),
          params: {
            from: from,
            to: to,
            resolution: 'day'
          },
          timeout: 10000
        }
      );

      return response.data;
    } catch (error) {
      logger.error('Failed to get API analytics:', error.message);
      throw new Error(`Tyk analytics retrieval failed: ${error.message}`);
    }
  }

  /**
   * Get key analytics from Tyk
   */
  async getKeyAnalytics(keyId, from, to) {
    try {
      const response = await axios.get(
        `${this.dashboardUrl}/api/usage/keys/${keyId}`,
        {
          headers: tykConfig.getDashboardHeaders(),
          params: {
            from: from,
            to: to,
            resolution: 'day'
          },
          timeout: 10000
        }
      );

      return response.data;
    } catch (error) {
      logger.error('Failed to get key analytics:', error.message);
      throw new Error(`Tyk key analytics retrieval failed: ${error.message}`);
    }
  }

  /**
   * Reload Tyk Gateway configuration
   */
  async reloadGateway() {
    try {
      await axios.get(
        `${this.gatewayUrl}/tyk/reload/group`,
        {
          headers: tykConfig.getGatewayHeaders(),
          timeout: 10000
        }
      );

      logger.info('Tyk Gateway configuration reloaded');
      return true;
    } catch (error) {
      logger.error('Failed to reload Tyk Gateway:', error.message);
      throw new Error(`Tyk Gateway reload failed: ${error.message}`);
    }
  }

  /**
   * Check Tyk Gateway health
   */
  async checkGatewayHealth() {
    try {
      const response = await axios.get(
        `${this.gatewayUrl}/hello`,
        {
          timeout: 5000
        }
      );

      return response.status === 200;
    } catch (error) {
      logger.debug('Tyk Gateway health check failed:', error.message);
      return false;
    }
  }

  /**
   * Check Tyk Dashboard health
   */
  async checkDashboardHealth() {
    try {
      const response = await axios.get(
        `${this.dashboardUrl}/hello`,
        {
          headers: tykConfig.getDashboardHeaders(),
          timeout: 5000
        }
      );

      return response.status === 200;
    } catch (error) {
      logger.debug('Tyk Dashboard health check failed:', error.message);
      return false;
    }
  }
}

module.exports = new TykService();
