const axios = require('axios');
const jwt = require('jsonwebtoken');
const moment = require('moment');
const tykConfig = require('../config/tyk');
const config = require('../config/config');
const logger = require('../config/logger');
const { v4: uuidv4 } = require('uuid');
const { withTykErrorHandling, withTykRetry, tykCircuitBreakers } = require('../helpers/tykError.helper');

/**
 * Tyk Service Layer
 * Handles all interactions with Tyk Gateway APIs
 */

const tykService = {
  gatewayUrl: tykConfig.gatewayUrl,
  orgId: tykConfig.orgId,

  /**
   * Create or update API definition in Tyk Gateway
   */
  async createOrUpdateApi(apiName, apiId = null) {
    const serviceCall = withTykRetry(
      withTykErrorHandling(async () => {
        const apiDefinition = tykConfig.generateApiDefinition(apiName, apiId || uuidv4());

        const response = await axios.post(
          `${this.gatewayUrl}/tyk/apis`,
          apiDefinition,
          {
            headers: tykConfig.getGatewayHeaders(),
            timeout: 10000
          }
        );

        logger.info(`API definition created/updated: ${apiName}`);
        return response.data;
      }, { service: 'gateway', operation: 'createOrUpdateApi' }),
      {
        maxRetries: 3,
        context: { service: 'gateway', operation: 'createOrUpdateApi' }
      }
    );

    return await tykCircuitBreakers.gateway.call(serviceCall, { service: 'gateway', operation: 'createOrUpdateApi' });
  },

  /**
   * Generate JWT token for Tyk authentication
   */
  generateTykJWT(identity, permissions = []) {
    const payload = {
      sub: identity.identity_id,
      iat: moment().unix(),
      exp: moment().add(config.jwt.accessExpirationMinutes, 'minutes').unix(),
      iss: 'caremate-api',
      aud: 'tyk-gateway',
      permissions: permissions,
      identity_type: identity.identity_type,
      facility_id: identity.facility_id,
      email: identity.email,
      first_name: identity.first_name,
      last_name: identity.last_name
    };

    return jwt.sign(payload, tykConfig.secret);
  },



  /**
   * Generate JWT token for Tyk authentication
   */
  generateTykJWT(identity, permissions = []) {
    const payload = {
      sub: identity.identity_id,
      iat: moment().unix(),
      exp: moment().add(config.jwt.accessExpirationMinutes, 'minutes').unix(),
      iss: 'caremate-api',
      aud: 'tyk-gateway',
      permissions: permissions,
      identity_type: identity.identity_type,
      facility_id: identity.facility_id,
      email: identity.email,
      first_name: identity.first_name,
      last_name: identity.last_name
    };

    return jwt.sign(payload, tykConfig.secret);
  },

  /**
   * Validate JWT token with Tyk
   */
  async validateJWT(token) {
    try {
      const decoded = jwt.verify(token, tykConfig.secret);
      return decoded;
    } catch (error) {
      logger.error('JWT validation failed:', error.message);
      throw new Error('Invalid or expired token');
    }
  },

  /**
   * Reload Tyk Gateway configuration
   */
  async reloadGateway() {
    try {
      await axios.get(
        `${this.gatewayUrl}/tyk/reload/group`,
        {
          headers: tykConfig.getGatewayHeaders(),
          timeout: 10000
        }
      );

      logger.info('Tyk Gateway configuration reloaded');
      return true;
    } catch (error) {
      logger.error('Failed to reload Tyk Gateway:', error.message);
      throw new Error(`Tyk Gateway reload failed: ${error.message}`);
    }
  },

  /**
   * Check Tyk Gateway health
   */
  async checkGatewayHealth() {
    try {
      const response = await axios.get(
        `${this.gatewayUrl}/hello`,
        {
          timeout: 5000
        }
      );

      return response.status === 200;
    } catch (error) {
      logger.debug('Tyk Gateway health check failed:', error.message);
      return false;
    }
  }
};

module.exports = tykService;
