# Tyk Integration Files Documentation

## 📁 Core Configuration Files

### `config/tyk.js`
**Purpose**: Main Tyk configuration and API definition generation
**What it does**:
- Validates Tyk environment variables
- Provides headers for Tyk API calls
- Generates API definitions for Tyk Gateway
- Generates policy definitions for roles/permissions
- **Used by**: All Tyk services and middlewares

### `config/config.js` (Updated)
**Purpose**: Environment variable validation and configuration
**What was added**:
- `AUTH` variable validation (custom/tyk)
- All Tyk environment variables (TYK_GATEWAY_URL, TYK_SECRET, etc.)
- Conditional validation (required only when AUTH=tyk)
- **Used by**: Entire application for configuration

### `config/tykSSO.js`
**Purpose**: SSO integration through Tyk Gateway
**What it does**:
- Replaces passport strategies when AUTH=tyk
- Configures SAML, OAuth, OIDC, Azure AD through Tyk
- Handles SSO callbacks and user extraction
- **Used by**: Authentication flows when SSO is needed

## 🔐 Authentication & Authorization Files

### `middlewares/tykAuth.js`
**Purpose**: Tyk authentication middleware
**What it does**:
- Validates JWT tokens using Tyk secret
- Introspects tokens with Tyk Gateway
- Checks user permissions against database
- **Used by**: Protected routes when AUTH=tyk

### `middlewares/auth.js` (Updated)
**Purpose**: Main authentication middleware with dual mode support
**What was changed**:
- Added conditional logic to use tykAuth when AUTH=tyk
- Maintains existing custom auth when AUTH=custom
- **Used by**: All protected routes in the application

### `services/tyk.service.js`
**Purpose**: Tyk API service layer
**What it does**:
- Creates/updates API definitions in Tyk
- Manages policies and API keys
- Generates JWT tokens for Tyk
- Health checks for Tyk services
- **Used by**: Controllers, policy manager, migration scripts

## 🛠️ Policy & User Management Files

### `utilities/tykPolicyManager.js`
**Purpose**: Dynamic policy management for roles/permissions
**What it does**:
- Converts database roles to Tyk policies
- Syncs permission changes to Tyk
- Creates API keys for users
- Manages policy cache
- **Used by**: When roles/permissions change, migration scripts

### `controllers/auth.controller.js` (Updated)
**Purpose**: Authentication controller with Tyk support
**What was changed**:
- Login/register methods support both auth modes
- Generates Tyk tokens when AUTH=tyk
- Creates Tyk policies for new users
- SSO callback handling for both modes
- **Used by**: Authentication routes (/auth/login, /auth/register, etc.)

## 🔄 Migration & Sync Files

### `scripts/migrate-to-tyk.js`
**Purpose**: One-time migration from custom to Tyk authentication
**What it does**:
- Migrates all existing roles to Tyk policies
- Creates API keys for existing users
- Validates migration success
- Provides rollback capability
- **Used by**: Manual execution when switching to Tyk mode

### `scripts/sync-tyk-policies.js`
**Purpose**: Ongoing synchronization of roles/policies
**What it does**:
- Syncs role changes to Tyk policies
- Updates user API keys
- Cleans up orphaned policies
- Validates sync status
- **Used by**: Scheduled tasks or manual execution

## 📊 Logging & Monitoring Files

### `config/morgan.js` (Updated)
**Purpose**: HTTP request logging with Tyk analytics
**What was changed**:
- Added Tyk analytics integration
- Sends request data to Tyk when AUTH=tyk
- Enhanced log format with auth mode and identity
- **Used by**: All HTTP requests for logging

### `config/logger.js` (Updated)
**Purpose**: Application logging with Tyk transport
**What was changed**:
- Added custom Tyk transport for logs
- Sends application logs to Tyk
- Enhanced logging methods for Tyk-specific events
- **Used by**: Entire application for logging

### `middlewares/rateLimiter.js` (Updated)
**Purpose**: Rate limiting with Tyk integration
**What was changed**:
- Uses Tyk rate limiting when AUTH=tyk
- Falls back to custom rate limiting if Tyk fails
- Checks rate limits with Tyk Gateway
- **Used by**: All API routes for rate limiting

## 🚨 Error Handling Files

### `helpers/tykError.helper.js`
**Purpose**: Comprehensive error handling for Tyk services
**What it does**:
- Defines Tyk-specific error types
- Handles connection, auth, rate limit errors
- Provides retry logic and circuit breaker
- **Used by**: All Tyk service calls

### `middlewares/error.js` (Updated)
**Purpose**: Global error handler with Tyk error support
**What was changed**:
- Added Tyk error handling before general errors
- Provides fallback mechanisms for Tyk failures
- **Used by**: All application errors

## 📦 Package & App Files

### `package.json` (Updated)
**Purpose**: Dependencies and scripts
**What was added**:
- `axios` dependency for Tyk API calls
- Tyk management scripts (migrate, sync, test)
- **Used by**: npm commands and application startup

### `app.js` (Updated)
**Purpose**: Application bootstrap with Tyk initialization
**What was changed**:
- Conditional Tyk SSO initialization
- Health monitoring startup
- Passport initialization only for custom mode
- **Used by**: Application startup

## 🎯 File Usage Summary

### **Always Used (Both Modes)**:
- `config/config.js` - Configuration validation
- `middlewares/auth.js` - Route protection
- `controllers/auth.controller.js` - Authentication endpoints

### **Only Used When AUTH=tyk**:
- `config/tyk.js` - Tyk configuration
- `middlewares/tykAuth.js` - Tyk authentication
- `services/tyk.service.js` - Tyk API calls
- `utilities/tykPolicyManager.js` - Policy management
- `config/tykSSO.js` - SSO integration

### **Migration/Maintenance Only**:
- `scripts/migrate-to-tyk.js` - One-time migration
- `scripts/sync-tyk-policies.js` - Ongoing sync

### **Enhanced for Tyk**:
- `config/morgan.js` - Dual logging
- `config/logger.js` - Tyk transport
- `middlewares/rateLimiter.js` - Dual rate limiting
- `helpers/tykError.helper.js` - Error handling
- `middlewares/error.js` - Error processing

## 🔧 How They Work Together

1. **Startup**: `app.js` reads `config/config.js` and initializes based on AUTH mode
2. **Authentication**: `middlewares/auth.js` routes to either custom or `middlewares/tykAuth.js`
3. **Tyk Calls**: `services/tyk.service.js` handles all Tyk API interactions
4. **Policy Management**: `utilities/tykPolicyManager.js` syncs roles to Tyk policies
5. **Error Handling**: `helpers/tykError.helper.js` manages Tyk-specific errors
6. **Logging**: Enhanced loggers send data to both local and Tyk systems

## 💡 Key Benefits

- **Modular**: Each file has a specific purpose
- **Conditional**: Tyk files only load when needed
- **Backward Compatible**: Custom auth continues to work
- **Maintainable**: Clear separation of concerns
- **Extensible**: Easy to add new Tyk features
