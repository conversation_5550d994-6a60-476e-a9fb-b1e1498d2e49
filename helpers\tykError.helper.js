const { status: httpStatus } = require('http-status');
const logger = require('../config/logger');
const config = require('../config/config');

/**
 * Tyk Error Helper
 * Comprehensive error handling for Tyk service failures
 */

class TykError extends Error {
  constructor(message, statusCode = 500, isOperational = true, stack = '') {
    super(message);
    this.name = 'TykError';
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.isTykError = true;
    
    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

class TykConnectionError extends TykError {
  constructor(service, originalError) {
    super(
      `Failed to connect to Tyk ${service}`,
      httpStatus.SERVICE_UNAVAILABLE,
      true
    );
    this.name = 'TykConnectionError';
    this.service = service;
    this.originalError = originalError;
  }
}

class TykAuthenticationError extends TykError {
  constructor(message = 'Tyk authentication failed') {
    super(message, httpStatus.UNAUTHORIZED, true);
    this.name = 'TykAuthenticationError';
  }
}

class TykAuthorizationError extends TykError {
  constructor(message = 'Tyk authorization failed') {
    super(message, httpStatus.FORBIDDEN, true);
    this.name = 'TykAuthorizationError';
  }
}

class TykRateLimitError extends TykError {
  constructor(retryAfter = null) {
    super('Rate limit exceeded', httpStatus.TOO_MANY_REQUESTS, true);
    this.name = 'TykRateLimitError';
    this.retryAfter = retryAfter;
  }
}

class TykPolicyError extends TykError {
  constructor(message, policyId = null) {
    super(message, httpStatus.BAD_REQUEST, true);
    this.name = 'TykPolicyError';
    this.policyId = policyId;
  }
}

class TykApiKeyError extends TykError {
  constructor(message, keyId = null) {
    super(message, httpStatus.BAD_REQUEST, true);
    this.name = 'TykApiKeyError';
    this.keyId = keyId;
  }
}

/**
 * Error handler for Tyk service calls
 */
const handleTykServiceError = (error, context = {}) => {
  const { service, operation, fallbackEnabled = true } = context;
  
  logger.error('Tyk service error:', {
    service,
    operation,
    error: error.message,
    stack: error.stack,
    context
  });

  // Handle different types of errors
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
    return new TykConnectionError(service, error);
  }

  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 401:
        return new TykAuthenticationError(data?.message || 'Invalid Tyk credentials');
      
      case 403:
        return new TykAuthorizationError(data?.message || 'Insufficient Tyk permissions');
      
      case 429:
        const retryAfter = error.response.headers['retry-after'];
        return new TykRateLimitError(retryAfter);
      
      case 404:
        return new TykError(`Tyk resource not found: ${data?.message || 'Unknown resource'}`, status);
      
      case 400:
        return new TykError(`Tyk bad request: ${data?.message || 'Invalid request'}`, status);
      
      case 500:
      case 502:
      case 503:
      case 504:
        return new TykConnectionError(service, error);
      
      default:
        return new TykError(`Tyk service error: ${data?.message || error.message}`, status);
    }
  }

  // Generic Tyk error
  return new TykError(`Tyk ${operation} failed: ${error.message}`, httpStatus.INTERNAL_SERVER_ERROR);
};

/**
 * Middleware to handle Tyk errors
 */
const tykErrorHandler = (error, req, res, next) => {
  if (!error.isTykError) {
    return next(error);
  }

  logger.error('Tyk error handled:', {
    name: error.name,
    message: error.message,
    statusCode: error.statusCode,
    url: req.originalUrl,
    method: req.method,
    identity: req.identity?.identity_id || 'anonymous'
  });

  // Add Tyk error headers
  res.set({
    'X-Tyk-Error': error.name,
    'X-Tyk-Error-Message': error.message
  });

  // Handle specific error types
  if (error instanceof TykRateLimitError && error.retryAfter) {
    res.set('Retry-After', error.retryAfter);
  }

  // Determine if we should fallback to custom auth
  const shouldFallback = 
    config.auth.mode === 'tyk' && 
    (error instanceof TykConnectionError || error instanceof TykAuthenticationError) &&
    req.path !== '/tyk-admin'; // Don't fallback for admin routes

  if (shouldFallback) {
    logger.warn('Falling back to custom authentication due to Tyk error');
    
    // Set fallback flag
    req.tykFallback = true;
    
    // Continue with custom auth middleware
    return next();
  }

  // Send error response
  res.status(error.statusCode).json({
    status: false,
    message: error.message,
    error: {
      type: error.name,
      service: error.service || 'tyk',
      timestamp: new Date().toISOString()
    }
  });
};

/**
 * Wrapper for Tyk service calls with error handling
 */
const withTykErrorHandling = (serviceCall, context = {}) => {
  return async (...args) => {
    try {
      return await serviceCall(...args);
    } catch (error) {
      throw handleTykServiceError(error, context);
    }
  };
};

/**
 * Retry wrapper for Tyk service calls
 */
const withTykRetry = (serviceCall, options = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    retryCondition = (error) => error instanceof TykConnectionError,
    context = {}
  } = options;

  return async (...args) => {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await serviceCall(...args);
      } catch (error) {
        lastError = handleTykServiceError(error, context);
        
        if (attempt === maxRetries || !retryCondition(lastError)) {
          throw lastError;
        }
        
        logger.warn(`Tyk service call failed, retrying (${attempt}/${maxRetries})`, {
          error: error.message,
          context,
          nextRetryIn: retryDelay * attempt
        });
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }
    
    throw lastError;
  };
};

/**
 * Circuit breaker for Tyk services
 */
class TykCircuitBreaker {
  constructor(options = {}) {
    this.failureThreshold = options.failureThreshold || 5;
    this.resetTimeout = options.resetTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 seconds
    
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.nextAttemptTime = null;
  }

  async call(serviceCall, context = {}) {
    if (this.state === 'OPEN') {
      if (Date.now() < this.nextAttemptTime) {
        throw new TykError('Circuit breaker is OPEN', httpStatus.SERVICE_UNAVAILABLE);
      }
      this.state = 'HALF_OPEN';
    }

    try {
      const result = await serviceCall();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw handleTykServiceError(error, context);
    }
  }

  onSuccess() {
    this.failureCount = 0;
    this.state = 'CLOSED';
    this.nextAttemptTime = null;
  }

  onFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
      this.nextAttemptTime = Date.now() + this.resetTimeout;
      
      logger.error('Tyk circuit breaker opened', {
        failureCount: this.failureCount,
        resetTimeout: this.resetTimeout
      });
    }
  }

  getState() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      nextAttemptTime: this.nextAttemptTime
    };
  }
}

/**
 * Global circuit breakers for Tyk services
 */
const tykCircuitBreakers = {
  gateway: new TykCircuitBreaker({ failureThreshold: 3, resetTimeout: 30000 }),
  dashboard: new TykCircuitBreaker({ failureThreshold: 3, resetTimeout: 30000 }),
  auth: new TykCircuitBreaker({ failureThreshold: 5, resetTimeout: 60000 })
};

/**
 * Get circuit breaker status
 */
const getCircuitBreakerStatus = () => {
  return Object.keys(tykCircuitBreakers).reduce((status, service) => {
    status[service] = tykCircuitBreakers[service].getState();
    return status;
  }, {});
};

module.exports = {
  TykError,
  TykConnectionError,
  TykAuthenticationError,
  TykAuthorizationError,
  TykRateLimitError,
  TykPolicyError,
  TykApiKeyError,
  handleTykServiceError,
  tykErrorHandler,
  withTykErrorHandling,
  withTykRetry,
  TykCircuitBreaker,
  tykCircuitBreakers,
  getCircuitBreakerStatus
};
