const { status: httpStatus } = require('http-status');
const { sendSuccess, sendError, catchAsync } = require('../helpers/api.helper');
const tykService = require('../services/tyk.service');
const config = require('../config/config');
const logger = require('../config/logger');
const moment = require('moment');

/**
 * Tyk Analytics Controller
 * Provides analytics and reporting endpoints for Tyk data
 */

/**
 * Get dashboard overview
 */
const getDashboardOverview = catchAsync(async (req, res) => {
  const { from, to } = req.query;
  
  const fromDate = from ? moment(from) : moment().subtract(24, 'hours');
  const toDate = to ? moment(to) : moment();

  // Get basic analytics data
  const overview = {
    period: {
      from: fromDate.toISOString(),
      to: toDate.toISOString(),
      duration: toDate.diff(fromDate, 'hours') + ' hours'
    },
    summary: {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      uniqueUsers: 0,
      topEndpoints: [],
      errorRate: 0
    },
    health: {
      gateway: await tykService.checkGatewayHealth(),
      dashboard: await tykService.checkDashboardHealth()
    },
    timestamp: new Date().toISOString()
  };

  // Note: In a real implementation, you would fetch actual analytics data from Tyk
  // For now, we'll return the structure with placeholder data

  sendSuccess(res, 'Analytics dashboard overview retrieved', httpStatus.OK, overview);
});

/**
 * Get request analytics
 */
const getRequestAnalytics = catchAsync(async (req, res) => {
  const { from, to, granularity = 'hour' } = req.query;
  
  const fromDate = from ? moment(from) : moment().subtract(24, 'hours');
  const toDate = to ? moment(to) : moment();

  const analytics = {
    period: {
      from: fromDate.toISOString(),
      to: toDate.toISOString()
    },
    granularity,
    data: {
      requests: [],
      responses: [],
      errors: [],
      latency: []
    },
    summary: {
      totalRequests: 0,
      averageLatency: 0,
      errorRate: 0,
      peakRPS: 0
    }
  };

  // Generate sample data points based on granularity
  const dataPoints = [];
  const current = moment(fromDate);
  
  while (current.isBefore(toDate)) {
    dataPoints.push({
      timestamp: current.toISOString(),
      requests: Math.floor(Math.random() * 1000),
      errors: Math.floor(Math.random() * 50),
      latency: Math.floor(Math.random() * 500) + 100
    });
    
    current.add(1, granularity);
  }

  analytics.data.requests = dataPoints.map(dp => ({
    timestamp: dp.timestamp,
    value: dp.requests
  }));

  analytics.data.errors = dataPoints.map(dp => ({
    timestamp: dp.timestamp,
    value: dp.errors
  }));

  analytics.data.latency = dataPoints.map(dp => ({
    timestamp: dp.timestamp,
    value: dp.latency
  }));

  sendSuccess(res, 'Request analytics retrieved', httpStatus.OK, analytics);
});

/**
 * Get user analytics
 */
const getUserAnalytics = catchAsync(async (req, res) => {
  const { from, to, limit = 50 } = req.query;
  
  const fromDate = from ? moment(from) : moment().subtract(24, 'hours');
  const toDate = to ? moment(to) : moment();

  const userAnalytics = {
    period: {
      from: fromDate.toISOString(),
      to: toDate.toISOString()
    },
    summary: {
      totalUsers: 0,
      activeUsers: 0,
      newUsers: 0,
      topUsers: []
    },
    users: [],
    pagination: {
      limit: parseInt(limit),
      total: 0,
      page: 1
    }
  };

  sendSuccess(res, 'User analytics retrieved', httpStatus.OK, userAnalytics);
});

/**
 * Get endpoint analytics
 */
const getEndpointAnalytics = catchAsync(async (req, res) => {
  const { from, to, sort = 'requests', order = 'desc' } = req.query;
  
  const fromDate = from ? moment(from) : moment().subtract(24, 'hours');
  const toDate = to ? moment(to) : moment();

  const endpointAnalytics = {
    period: {
      from: fromDate.toISOString(),
      to: toDate.toISOString()
    },
    endpoints: [
      {
        path: '/facility',
        method: 'GET',
        requests: 1250,
        errors: 15,
        averageLatency: 145,
        errorRate: 1.2
      },
      {
        path: '/auth/login',
        method: 'POST',
        requests: 890,
        errors: 8,
        averageLatency: 230,
        errorRate: 0.9
      },
      {
        path: '/identity',
        method: 'GET',
        requests: 675,
        errors: 12,
        averageLatency: 180,
        errorRate: 1.8
      }
    ],
    summary: {
      totalEndpoints: 3,
      totalRequests: 2815,
      totalErrors: 35,
      averageLatency: 185
    }
  };

  // Sort endpoints
  endpointAnalytics.endpoints.sort((a, b) => {
    const aVal = a[sort];
    const bVal = b[sort];
    return order === 'desc' ? bVal - aVal : aVal - bVal;
  });

  sendSuccess(res, 'Endpoint analytics retrieved', httpStatus.OK, endpointAnalytics);
});

/**
 * Get error analytics
 */
const getErrorAnalytics = catchAsync(async (req, res) => {
  const { from, to, groupBy = 'status' } = req.query;
  
  const fromDate = from ? moment(from) : moment().subtract(24, 'hours');
  const toDate = to ? moment(to) : moment();

  const errorAnalytics = {
    period: {
      from: fromDate.toISOString(),
      to: toDate.toISOString()
    },
    groupBy,
    errors: [
      {
        type: '401 Unauthorized',
        count: 45,
        percentage: 35.7,
        trend: 'increasing'
      },
      {
        type: '403 Forbidden',
        count: 32,
        percentage: 25.4,
        trend: 'stable'
      },
      {
        type: '429 Too Many Requests',
        count: 28,
        percentage: 22.2,
        trend: 'decreasing'
      },
      {
        type: '500 Internal Server Error',
        count: 21,
        percentage: 16.7,
        trend: 'stable'
      }
    ],
    summary: {
      totalErrors: 126,
      errorRate: 4.5,
      mostCommonError: '401 Unauthorized',
      trend: 'increasing'
    }
  };

  sendSuccess(res, 'Error analytics retrieved', httpStatus.OK, errorAnalytics);
});

/**
 * Get performance analytics
 */
const getPerformanceAnalytics = catchAsync(async (req, res) => {
  const { from, to, metric = 'latency' } = req.query;
  
  const fromDate = from ? moment(from) : moment().subtract(24, 'hours');
  const toDate = to ? moment(to) : moment();

  const performanceAnalytics = {
    period: {
      from: fromDate.toISOString(),
      to: toDate.toISOString()
    },
    metric,
    data: {
      average: 185,
      median: 165,
      p95: 450,
      p99: 890,
      min: 45,
      max: 2340
    },
    trends: {
      hourly: [],
      daily: []
    },
    summary: {
      status: 'good',
      improvement: '+12%',
      alerts: []
    }
  };

  sendSuccess(res, 'Performance analytics retrieved', httpStatus.OK, performanceAnalytics);
});

/**
 * Get real-time metrics
 */
const getRealTimeMetrics = catchAsync(async (req, res) => {
  const realTimeMetrics = {
    timestamp: new Date().toISOString(),
    metrics: {
      currentRPS: Math.floor(Math.random() * 100) + 50,
      activeConnections: Math.floor(Math.random() * 500) + 200,
      averageLatency: Math.floor(Math.random() * 200) + 100,
      errorRate: (Math.random() * 5).toFixed(2),
      memoryUsage: (Math.random() * 80 + 20).toFixed(1),
      cpuUsage: (Math.random() * 60 + 10).toFixed(1)
    },
    health: {
      gateway: await tykService.checkGatewayHealth(),
      dashboard: await tykService.checkDashboardHealth(),
      overall: 'healthy'
    },
    alerts: []
  };

  sendSuccess(res, 'Real-time metrics retrieved', httpStatus.OK, realTimeMetrics);
});

/**
 * Export analytics data
 */
const exportAnalytics = catchAsync(async (req, res) => {
  const { from, to, format = 'json', type = 'summary' } = req.query;
  
  const fromDate = from ? moment(from) : moment().subtract(24, 'hours');
  const toDate = to ? moment(to) : moment();

  const exportData = {
    metadata: {
      exportedAt: new Date().toISOString(),
      period: {
        from: fromDate.toISOString(),
        to: toDate.toISOString()
      },
      format,
      type
    },
    data: {
      summary: {
        totalRequests: 15420,
        totalErrors: 234,
        averageLatency: 185,
        uniqueUsers: 1250
      }
    }
  };

  // Set appropriate headers for download
  res.set({
    'Content-Type': format === 'csv' ? 'text/csv' : 'application/json',
    'Content-Disposition': `attachment; filename="tyk-analytics-${fromDate.format('YYYY-MM-DD')}-to-${toDate.format('YYYY-MM-DD')}.${format}"`
  });

  if (format === 'csv') {
    // Convert to CSV format
    const csv = 'metric,value\n' + 
      Object.entries(exportData.data.summary)
        .map(([key, value]) => `${key},${value}`)
        .join('\n');
    
    res.send(csv);
  } else {
    res.json(exportData);
  }
});

module.exports = {
  getDashboardOverview,
  getRequestAnalytics,
  getUserAnalytics,
  getEndpointAnalytics,
  getErrorAnalytics,
  getPerformanceAnalytics,
  getRealTimeMetrics,
  exportAnalytics
};
