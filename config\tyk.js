const config = require('./config');
const logger = require('./logger');

/**
 * Tyk Configuration Module
 * Handles all Tyk-related configurations and utilities
 */

const tykConfig = {
  isEnabled: config.auth.mode === 'tyk',
  gatewayUrl: config.tyk.gatewayUrl,
  secret: config.tyk.secret,
  apiKey: config.tyk.apiKey,
  orgId: config.tyk.orgId,
  listenPath: config.tyk.listenPath,
  targetUrl: config.tyk.targetUrl,

  /**
   * Validate Tyk configuration
   */
  validateConfig() {
    const requiredFields = [
      'gatewayUrl',
      'secret',
      'apiKey',
      'orgId',
      'targetUrl'
    ];

    const missingFields = requiredFields.filter(field => !this[field]);

    if (missingFields.length > 0) {
      throw new Error(`Missing required Tyk configuration fields: ${missingFields.join(', ')}`);
    }

    logger.info('Tyk configuration validated successfully');
  },

  /**
   * Get Tyk Gateway headers for Gateway API calls
   */
  getGatewayHeaders() {
    return {
      'x-tyk-authorization': this.secret,
      'Content-Type': 'application/json'
    };
  },

  /**
   * Generate API definition for Tyk
   */
  generateApiDefinition(apiName, apiId) {
    return {
      name: apiName,
      api_id: apiId,
      org_id: this.orgId,
      use_keyless: false,
      use_oauth2: false,
      use_openid: false,
      openid_options: {},
      oauth_meta: {},
      auth: {
        use_param: false,
        param_name: "",
        use_cookie: false,
        cookie_name: "",
        auth_header_name: "Authorization",
        use_certificate: false
      },
      use_basic_auth: false,
      basic_auth: {
        disable_caching: false,
        cache_ttl: 0,
        extract_from_body: false,
        body_user_regexp: "",
        body_password_regexp: ""
      },
      use_mutual_tls: false,
      client_certificates: [],
      upstream_certificates: {},
      pinned_public_keys: {},
      enable_jwt: true,
      jwt_signing_method: "hmac",
      jwt_source: config.jwt.secret,
      jwt_identity_base_field: "sub",
      jwt_client_base_field: "",
      jwt_policy_field_name: "pol",
      jwt_default_policies: [],
      jwt_issued_at_validation_skew: 0,
      jwt_expires_at_validation_skew: 0,
      jwt_not_before_validation_skew: 0,
      jwt_skip_kid: false,
      jwt_scope_to_policy_mapping: {},
      jwt_scope_claim_name: "scope",
      notifications: {
        shared_secret: "",
        oauth_on_keychange_url: ""
      },
      enable_signature_checking: false,
      hmac_allowed_clock_skew: -1,
      hmac_allowed_algorithms: [],
      request_signing: {
        is_enabled: false,
        secret: "",
        key_id: "",
        algorithm: ""
      },
      base_identity_provided_by: "",
      definition: {
        location: "header",
        key: "x-api-version",
        strip_path: false
      },
      version_data: {
        not_versioned: true,
        default_version: "",
        versions: {
          "Default": {
            name: "Default",
            expires: "",
            paths: {
              ignored: [],
              white_list: [],
              black_list: []
            },
            use_extended_paths: true,
            extended_paths: {
              ignored: [],
              white_list: [],
              black_list: [],
              cache: [],
              transform: [],
              transform_response: [],
              transform_jq: [],
              transform_jq_response: [],
              transform_headers: [],
              transform_response_headers: [],
              hard_timeouts: [],
              circuit_breakers: [],
              url_rewrites: [],
              virtual: [],
              size_limits: [],
              method_transforms: [],
              track_endpoints: [],
              do_not_track_endpoints: [],
              validate_json: [],
              internal: []
            },
            global_headers: {},
            global_headers_remove: [],
            global_response_headers: {},
            global_response_headers_remove: [],
            ignore_endpoint_case: false,
            global_size_limit: 0,
            override_target: ""
          }
        }
      },
      proxy: {
        preserve_host_header: false,
        listen_path: this.listenPath,
        target_url: this.targetUrl,
        disable_strip_slash: true,
        strip_listen_path: true,
        enable_load_balancing: false,
        target_list: [],
        check_host_against_uptime_tests: false,
        service_discovery: {
          use_discovery_service: false,
          query_endpoint: "",
          use_nested_query: false,
          parent_data_path: "",
          data_path: "",
          cache_timeout: 60
        },
        transport: {
          ssl_insecure_skip_verify: false,
          ssl_ciphers: [],
          ssl_min_version: 0,
          ssl_force_common_name_check: false,
          proxy_url: ""
        }
      },
      disable_rate_limit: false,
      disable_quota: false,
      custom_middleware: {
        pre: [],
        post: [],
        post_key_auth: [],
        auth_check: {
          name: "",
          path: "",
          require_session: false,
          raw_body_only: false
        },
        response: [],
        driver: "",
        id_extractor: {
          extract_from: "",
          extract_with: "",
          extractor_config: {}
        }
      },
      custom_middleware_bundle: "",
      cache_options: {
        cache_timeout: 60,
        enable_cache: false,
        cache_all_safe_requests: false,
        cache_response_codes: [],
        enable_upstream_cache_control: false,
        cache_control_ttl_header: "",
        cache_by_headers: []
      },
      session_lifetime: 0,
      active: true,
      auth_provider: {
        name: "",
        storage_engine: "",
        meta: {}
      },
      session_provider: {
        name: "",
        storage_engine: "",
        meta: {}
      },
      event_handlers: {
        events: {}
      },
      enable_batch_request_support: false,
      enable_ip_whitelisting: false,
      allowed_ips: [],
      enable_ip_blacklisting: false,
      blacklisted_ips: [],
      dont_set_quota_on_create: false,
      expire_analytics_after: 0,
      response_processors: [],
      CORS: {
        enable: true,
        allowed_origins: ["*"],
        allowed_methods: ["GET", "POST", "HEAD", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allowed_headers: ["Origin", "Accept", "Content-Type", "X-Requested-With", "Authorization"],
        exposed_headers: [],
        allow_credentials: false,
        max_age: 24,
        options_passthrough: false,
        debug: false
      },
      domain: "",
      certificates: [],
      do_not_track: false,
      tags: ["api"],
      enable_context_vars: false,
      config_data: {},
      tag_headers: [],
      global_rate_limit: {
        rate: 0,
        per: 0
      },
      strip_auth_data: false
    };
  },

  /**
   * Generate policy definition for Tyk
   */
  generatePolicyDefinition(policyName, permissions = [], rateLimit = null, quota = null) {
    const policy = {
      name: policyName,
      active: true,
      org_id: this.orgId,
      rate: rateLimit ? rateLimit.rate : 1000,
      per: rateLimit ? rateLimit.per : 60,
      quota_max: quota ? quota.max : -1,
      quota_renews: quota ? quota.renews : 1577836800,
      quota_remaining: quota ? quota.remaining : -1,
      quota_renewal_rate: quota ? quota.renewalRate : 3600,
      access_rights: {},
      hmac_enabled: false,
      enable_http_signature_validation: false,
      is_inactive: false,
      apply_policy_request_size_limit: false,
      apply_policy_request_size_limit_value: 0,
      tags: ["policy"],
      key_expires_in: 0,
      partitions: {
        quota: false,
        rate_limit: false,
        complexity: false,
        acl: false
      },
      last_updated: new Date().toISOString(),
      meta_data: {
        permissions: permissions
      },
      graphql_access_rights: {},
      hook_references: [],
      is_per_api: false,
      apply_policies: []
    };

    return policy;
  }
};

// Initialize validation if Tyk is enabled
if (tykConfig.isEnabled) {
  tykConfig.validateConfig();
}

module.exports = tykConfig;
