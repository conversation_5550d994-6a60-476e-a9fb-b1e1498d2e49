#!/usr/bin/env node

/**
 * Migration Script: Custom to Tyk
 * Migrates existing roles, permissions, and users to Tyk policies and keys
 */

const { Role, Permission, Identity, IdentityRole } = require('../models');
const tykService = require('../services/tyk.service');
const tykPolicyManager = require('../utilities/tykPolicyManager');
const logger = require('../config/logger');
const config = require('../config/config');

class TykMigration {
  constructor() {
    this.migrationResults = {
      roles: { success: 0, failed: 0, errors: [] },
      policies: { success: 0, failed: 0, errors: [] },
      users: { success: 0, failed: 0, errors: [] },
      apiKeys: { success: 0, failed: 0, errors: [] }
    };
  }

  /**
   * Run complete migration
   */
  async runMigration() {
    console.log('🚀 Starting migration from Custom to Tyk authentication...\n');

    try {
      // Pre-migration checks
      await this.preMigrationChecks();

      // Step 1: Migrate roles to policies
      await this.migrateRolesToPolicies();

      // Step 2: Create API keys for existing users
      await this.createApiKeysForUsers();

      // Step 3: Verify migration
      await this.verifyMigration();

      // Print results
      this.printMigrationResults();

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      logger.error('Migration failed:', error);
      process.exit(1);
    }
  }

  /**
   * Pre-migration checks
   */
  async preMigrationChecks() {
    console.log('🔍 Running pre-migration checks...');

    // Check if Tyk is configured
    if (config.auth.mode !== 'tyk') {
      throw new Error('AUTH mode must be set to "tyk" for migration');
    }

    // Check Tyk connectivity
    const gatewayHealth = await tykService.checkGatewayHealth();
    if (!gatewayHealth) {
      throw new Error('Tyk Gateway is not accessible');
    }

    const dashboardHealth = await tykService.checkDashboardHealth();
    if (!dashboardHealth) {
      throw new Error('Tyk Dashboard is not accessible');
    }

    // Check database connectivity
    const roleCount = await Role.count();
    const userCount = await Identity.count();

    console.log(`✅ Pre-migration checks passed`);
    console.log(`   - Found ${roleCount} roles to migrate`);
    console.log(`   - Found ${userCount} users to process`);
    console.log(`   - Tyk Gateway: Online`);
    console.log(`   - Tyk Dashboard: Online\n`);
  }

  /**
   * Migrate roles to Tyk policies
   */
  async migrateRolesToPolicies() {
    console.log('📋 Migrating roles to Tyk policies...');

    try {
      const roles = await Role.findAll({
        where: { is_active: true },
        include: [
          {
            model: Permission,
            as: 'permission',
            through: { attributes: [] }
          }
        ]
      });

      for (const role of roles) {
        try {
          const permissions = role.permission || [];
          const result = await tykPolicyManager.createRolePolicy(role, permissions);

          if (result) {
            this.migrationResults.roles.success++;
            this.migrationResults.policies.success++;
            console.log(`   ✅ Migrated role: ${role.name}`);
          } else {
            throw new Error('Policy creation returned null');
          }

        } catch (error) {
          this.migrationResults.roles.failed++;
          this.migrationResults.policies.failed++;
          this.migrationResults.roles.errors.push({
            role: role.name,
            error: error.message
          });
          console.log(`   ❌ Failed to migrate role: ${role.name} - ${error.message}`);
        }
      }

      console.log(`✅ Role migration completed: ${this.migrationResults.roles.success} success, ${this.migrationResults.roles.failed} failed\n`);

    } catch (error) {
      console.error('❌ Role migration failed:', error.message);
      throw error;
    }
  }

  /**
   * Create API keys for existing users
   */
  async createApiKeysForUsers() {
    console.log('🔑 Creating API keys for existing users...');

    try {
      const users = await Identity.findAll({
        include: [
          {
            model: Role,
            as: 'role',
            through: { attributes: [] }
          }
        ]
      });

      for (const user of users) {
        try {
          const roles = user.role || [];
          
          if (roles.length === 0) {
            console.log(`   ⚠️  Skipping user ${user.email}: No roles assigned`);
            continue;
          }

          const result = await tykPolicyManager.createIdentityApiKey(user, roles);

          if (result) {
            this.migrationResults.users.success++;
            this.migrationResults.apiKeys.success++;
            console.log(`   ✅ Created API key for user: ${user.email}`);
          } else {
            throw new Error('API key creation returned null');
          }

        } catch (error) {
          this.migrationResults.users.failed++;
          this.migrationResults.apiKeys.failed++;
          this.migrationResults.users.errors.push({
            user: user.email,
            error: error.message
          });
          console.log(`   ❌ Failed to create API key for user: ${user.email} - ${error.message}`);
        }
      }

      console.log(`✅ API key creation completed: ${this.migrationResults.users.success} success, ${this.migrationResults.users.failed} failed\n`);

    } catch (error) {
      console.error('❌ API key creation failed:', error.message);
      throw error;
    }
  }

  /**
   * Verify migration
   */
  async verifyMigration() {
    console.log('🔍 Verifying migration...');

    try {
      // Check if policies were created
      const roleCount = await Role.count({ where: { is_active: true } });
      const policyCount = this.migrationResults.policies.success;

      console.log(`   - Roles in database: ${roleCount}`);
      console.log(`   - Policies created in Tyk: ${policyCount}`);

      // Check if API keys were created
      const userCount = await Identity.count();
      const apiKeyCount = this.migrationResults.apiKeys.success;

      console.log(`   - Users in database: ${userCount}`);
      console.log(`   - API keys created in Tyk: ${apiKeyCount}`);

      // Test a sample authentication
      try {
        const sampleUser = await Identity.findOne({
          include: [{ model: Role, as: 'role' }]
        });

        if (sampleUser && sampleUser.role && sampleUser.role.length > 0) {
          const permissions = await tykPolicyManager.getRolePermissions(sampleUser.role[0].role_id);
          const testToken = tykService.generateTykJWT(sampleUser, permissions.map(p => p.name));
          const validatedToken = await tykService.validateJWT(testToken);

          if (validatedToken) {
            console.log(`   ✅ Sample authentication test passed`);
          }
        }
      } catch (error) {
        console.log(`   ⚠️  Sample authentication test failed: ${error.message}`);
      }

      console.log(`✅ Migration verification completed\n`);

    } catch (error) {
      console.error('❌ Migration verification failed:', error.message);
      throw error;
    }
  }

  /**
   * Print migration results
   */
  printMigrationResults() {
    console.log('📊 Migration Results Summary:');
    console.log('=' * 50);

    console.log('\nRoles & Policies:');
    console.log(`  ✅ Successfully migrated: ${this.migrationResults.roles.success}`);
    console.log(`  ❌ Failed: ${this.migrationResults.roles.failed}`);

    console.log('\nUsers & API Keys:');
    console.log(`  ✅ Successfully processed: ${this.migrationResults.users.success}`);
    console.log(`  ❌ Failed: ${this.migrationResults.users.failed}`);

    if (this.migrationResults.roles.errors.length > 0) {
      console.log('\nRole Migration Errors:');
      this.migrationResults.roles.errors.forEach(error => {
        console.log(`  - ${error.role}: ${error.error}`);
      });
    }

    if (this.migrationResults.users.errors.length > 0) {
      console.log('\nUser Migration Errors:');
      this.migrationResults.users.errors.forEach(error => {
        console.log(`  - ${error.user}: ${error.error}`);
      });
    }

    const totalSuccess = this.migrationResults.roles.success + this.migrationResults.users.success;
    const totalFailed = this.migrationResults.roles.failed + this.migrationResults.users.failed;
    const totalItems = totalSuccess + totalFailed;

    console.log('\n' + '=' * 50);
    console.log(`Total Items Processed: ${totalItems}`);
    console.log(`Total Success: ${totalSuccess}`);
    console.log(`Total Failed: ${totalFailed}`);

    if (totalItems > 0) {
      console.log(`Success Rate: ${((totalSuccess / totalItems) * 100).toFixed(1)}%`);
    }

    if (totalFailed === 0) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('You can now use Tyk authentication mode.');
      console.log('\nNext steps:');
      console.log('1. Update your application to use AUTH=tyk');
      console.log('2. Test authentication flows');
      console.log('3. Monitor Tyk analytics');
    } else {
      console.log('\n⚠️  Migration completed with some failures.');
      console.log('Please review the errors above and retry failed items manually.');
    }
  }

  /**
   * Rollback migration (if needed)
   */
  async rollbackMigration() {
    console.log('🔄 Rolling back migration...');
    
    try {
      // Clear policy cache
      tykPolicyManager.clearCache();
      
      // Note: Actual Tyk policy/key deletion would need to be implemented
      // based on your specific requirements and Tyk setup
      
      console.log('✅ Migration rollback completed');
    } catch (error) {
      console.error('❌ Rollback failed:', error.message);
      throw error;
    }
  }
}

// CLI interface
if (require.main === module) {
  const migration = new TykMigration();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'migrate':
      migration.runMigration().catch(error => {
        console.error('Migration failed:', error);
        process.exit(1);
      });
      break;
      
    case 'rollback':
      migration.rollbackMigration().catch(error => {
        console.error('Rollback failed:', error);
        process.exit(1);
      });
      break;
      
    default:
      console.log('Usage:');
      console.log('  node scripts/migrate-to-tyk.js migrate   - Run migration');
      console.log('  node scripts/migrate-to-tyk.js rollback - Rollback migration');
      process.exit(1);
  }
}

module.exports = TykMigration;
