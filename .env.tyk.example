# Tyk Integration Configuration Example
# Copy this to your .env file and update the values

# Authentication Mode (custom or tyk)
AUTH=tyk

# Tyk Gateway Configuration
TYK_GATEWAY_URL=http://localhost:8080
TYK_SECRET=your-tyk-gateway-secret-here

# Tyk Dashboard Configuration  
TYK_DASHBOARD_URL=http://localhost:3000
TYK_API_KEY=your-tyk-dashboard-api-key-here
TYK_ORG_ID=your-tyk-organization-id-here

# Tyk API Configuration
TYK_LISTEN_PATH=/api/v1
TYK_TARGET_URL=http://localhost:3001

# Existing Configuration (keep your current values)
NODE_ENV=development
PORT=3000

# Database Configuration
DB_DIALECT=postgres
DB_WRITE_HOST=localhost
DB_WRITE_USERNAME=your_db_user
DB_WRITE_PASSWORD=your_db_password
DB_WRITE_DATABASE=your_db_name
DB_READ_HOST=localhost
DB_READ_USERNAME=your_db_user
DB_READ_PASSWORD=your_db_password
DB_READ_DATABASE=your_db_name
DB_PORT=5432
DB_LOGGING=false

# JWT Configuration
JWT_SECRET=your-jwt-secret-here
JWT_ACCESS_EXPIRATION_MINUTES=30
JWT_REFRESH_EXPIRATION_DAYS=30

# Logging Configuration
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=30d
LOG_FILE_TRANSPORT=false

# SAML Configuration (for SSO)
SAML_ENTRY_POINT=https://your-saml-provider.com/sso
SAML_ISSUER=your-app-issuer
SAML_CERT=your-saml-certificate
SAML_CALLBACK_URL=http://localhost:3000/auth/saml/callback

# OIDC Configuration (for SSO)
OIDC_ISSUER=https://your-oidc-provider.com
OIDC_AUTHORIZATION_URL=https://your-oidc-provider.com/auth
OIDC_TOKEN_URL=https://your-oidc-provider.com/token
OIDC_USERINFO_URL=https://your-oidc-provider.com/userinfo
OIDC_CLIENT_ID=your-oidc-client-id
OIDC_CLIENT_SECRET=your-oidc-client-secret
OIDC_CALLBACK_URL=http://localhost:3000/auth/oidc/callback

# Azure AD Configuration (for SSO)
AZURE_TENANT_ID=your-azure-tenant-id
AZURE_CLIENT_ID=your-azure-client-id
AZURE_CLIENT_SECRET=your-azure-client-secret
AZURE_CALLBACK_URL=http://localhost:3000/auth/azure/callback

# Message Queuing
MESSAGE_QUEUING=false
RABBITMQ_URL=amqp://localhost

# Message Processing
CONCURRENCY_LIMIT=10
COLLECTION_TIMEOUT=5000
SHUTDOWN_TIMEOUT=10000

# Caching
CACHE_DRIVER=memory
CACHE_TTL=3600
REDIS_HOST=localhost
REDIS_PORT=6379
MEMCACHED_HOST=localhost

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# Telnyx Configuration
TELNYX_API_KEY=your-telnyx-api-key
TELNYX_PHONE_NUMBER=your-telnyx-phone-number
